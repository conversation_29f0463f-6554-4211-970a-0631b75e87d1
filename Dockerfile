# Use the official Node.js runtime as the base image
FROM node:18-alpine

# Set the working directory inside the container
WORKDIR /app

# Copy package.json and package-lock.json (if available)
COPY package*.json ./

# Install dependencies for production
RUN npm ci --only=production

# Create a non-root user to run the application
RUN addgroup -g 1001 -S nodejs
RUN adduser -S nodejs -u 1001 -G nodejs

# Copy the rest of the application code
COPY . .

# Copy production environment file as .env
COPY production.env .env

# Change ownership of the app directory to the nodejs user
RUN chown -R nodejs:nodejs /app
USER nodejs

# Expose port 3002 for production (as per production.env)
EXPOSE 3002

# Define environment variable
ENV NODE_ENV=production

# Command to run the application
CMD ["node", "index.js"]
