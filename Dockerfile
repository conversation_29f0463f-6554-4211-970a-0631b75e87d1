# Use the official Node.js runtime as the base image
FROM node:18-alpine

# Build argument to specify the environment (development, testing, production)
ARG BUILD_ENV=production

# Set the working directory inside the container
WORKDIR /app

# Copy package.json and package-lock.json (if available)
COPY package*.json ./

# Install dependencies based on environment
RUN if [ "$BUILD_ENV" = "production" ]; then \
        npm ci --only=production; \
    else \
        npm ci; \
    fi

# Create a non-root user to run the application
RUN addgroup -g 1001 -S nodejs
RUN adduser -S nodejs -u 1001 -G nodejs

# Copy the rest of the application code
COPY . .

# Copy the appropriate environment file based on BUILD_ENV
RUN if [ "$BUILD_ENV" = "development" ]; then \
        cp development.env .env; \
    elif [ "$BUILD_ENV" = "testing" ]; then \
        cp testing.env .env; \
    elif [ "$BUILD_ENV" = "production" ]; then \
        cp production.env .env; \
    fi

# Change ownership of the app directory to the nodejs user
RUN chown -R nodejs:nodejs /app
USER nodejs

# Expose different ports based on environment
# Development: 3000, Testing: 3001, Production: 3002
EXPOSE 3000 3001 3002

# Define environment variable
ENV NODE_ENV=$BUILD_ENV

# Command to run the application
CMD ["node", "index.js"]
