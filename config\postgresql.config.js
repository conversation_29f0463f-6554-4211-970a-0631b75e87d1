// /config/postgresql.config.js
import pg from "pg";
import "dotenv/config";
import config from "../config.js";

const { Pool } = pg;

const postgreSqlPool = new Pool({
  host: config.DB_HOST,
  user: config.DB_USER,
  port: config.DB_PORT, // Ensure port is number
  password: config.DB_PASSWORD,
  database: config.DB_NAME,
});

const connectPostgreSQL = async () => {
  try {
    const client = await postgreSqlPool.connect();
    client.release();
    console.log(`✅ PostgreSQL database connected successfully`);
  } catch (error) {
    console.error(`❌ PostgreSQL connection error: ${error.message}`);
  }
};

postgreSqlPool.on("error", (err) => {
  console.error(`❌ PostgreSQL pool error:`, err);
});

export { postgreSqlPool, connectPostgreSQL };
