export default {
  contactInquiry: `
    CREATE TABLE IF NOT EXISTS contact_inquiries (
      id SERIAL PRIMARY KEY,
      name VA<PERSON>HA<PERSON>(100) NOT NULL,
      phone_number VARCHAR(15),
      email VARCHAR(100) NOT NULL,
      class_studying VARCHAR(50),
      description TEXT,
      inquiry_type VARCHAR(20) NOT NULL CHECK (inquiry_type IN ('student', 'business')),
      created_at VARCHAR(50),
    );
  `,
};
