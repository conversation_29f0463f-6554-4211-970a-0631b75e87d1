import nodemailer from "nodemailer";
import { postgreSqlPool } from "../config/postgresql.config.js";
import config from "../config.js";

const transporter = nodemailer.createTransport({
  host: config.MAILHOST,
  port: 587,
  secure: false,
  auth: {
    user: config.MAIL_USER,
    pass: config.MAIL_PASS,
  },
});

export const submitStudentInquiry = async (req, res) => {
  const { name, phoneNumber, email, classStudying, description } = req.body;

  if (!name || !phoneNumber || !email || !classStudying || !description) {
    return res.status(400).json({ error: "All fields are required" });
  }

  try {
    const query = `
      INSERT INTO contact_inquiries (name, phone_number, email, class_studying, description, inquiry_type, created_at)
      VALUES ($1, $2, $3, $4, $5, $6, $7) RETURNING *`;
    const values = [
      name,
      phoneNumber,
      email,
      classStudying,
      description,
      "student",
      new Date().toISOString(),
    ];
    const result = await postgreSqlPool.query(query, values);

    await transporter.sendMail({
      from: `"Sasthra Academy" <${config.MAIL_USER}>`,
      replyTo: email,
      to: config.MAIL_TO,
      subject: `🎓 New Student Inquiry: ${name}`,
      html: `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="UTF-8">
        <title>New Student Inquiry</title>
        <style>
          @import url('https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;600;700&display=swap');
          
          body {
            font-family: 'Poppins', sans-serif;
            background-color: #f8f9fa;
            margin: 0;
            padding: 0;
          }
          
          .email-container {
            max-width: 600px;
            margin: 0 auto;
            background: linear-gradient(135deg, #ffffff 0%, #f5f7fa 100%);
            border-radius: 16px;
            overflow: hidden;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.08);
          }
          
          .header {
            background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
            padding: 30px;
            text-align: center;
            color: white;
          }
          
          .header h1 {
            margin: 0;
            font-size: 24px;
            font-weight: 700;
          }
          
          .header-icon {
            font-size: 40px;
            margin-bottom: 15px;
            display: inline-block;
          }
          
          .content {
            padding: 30px;
          }
          
          .details-table {
            width: 100%;
            border-collapse: separate;
            border-spacing: 0 10px;
          }
          
          .details-table td {
            padding: 12px 15px;
            vertical-align: top;
          }
          
          .details-table tr td:first-child {
            font-weight: 600;
            color: #4b5563;
            width: 35%;
            border-right: 1px solid #e5e7eb;
          }
          
          .details-table tr {
            background-color: #ffffff;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.03);
          }
          
          .message-box {
            background-color: #ffffff;
            border-left: 4px solid #3b82f6;
            padding: 15px;
            margin-top: 20px;
            border-radius: 0 8px 8px 0;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.03);
          }
          
          .footer {
            text-align: center;
            padding: 20px;
            color: #6b7280;
            font-size: 12px;
            background-color: #f9fafb;
            border-top: 1px solid #e5e7eb;
          }
          
          .action-button {
            display: inline-block;
            margin-top: 20px;
            padding: 12px 24px;
            background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
            color: white;
            text-decoration: none;
            border-radius: 50px;
            font-weight: 600;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
          }
        </style>
      </head>
      <body>
        <div class="email-container">
          <div class="header">
            <div class="header-icon">🎓</div>
            <h1> Student Inquiry</h1>
          </div>
          
          <div class="content">
            <table class="details-table">
              <tr>
                <td> Name</td>
                <td>${name}</td>
              </tr>
              <tr>
                <td>Contact</td>
                <td>${phoneNumber}</td>
              </tr>
              <tr>
                <td>Email </td>
                <td><a href="mailto:${email}">${email}</a></td>
              </tr>
              <tr>
                <td>Class</td>
                <td>${classStudying}</td>
              </tr>
            </table>
            
            <div class="message-box">
              <h3 style="margin-top: 0; color: #1e40af;">Student's Message:</h3>
              <p style="margin-bottom: 0; color: #4b5563; line-height: 1.6;">${description}</p>
            </div>
            
            <div style="text-align: center; margin-top: 30px;">
              <a href="mailto:${email}" class="action-button">Reply to Student</a>
            </div>
          </div>
          
          <div class="footer">
            <p>This inquiry was submitted through Sasthra Academy's contact form.</p>
            <p> ${new Date().getFullYear()} Sasthra Academy. All rights reserved.</p>
          </div>
        </div>
      </body>
      </html>
    `,
    });

    res.status(201).json({
      message: "Student inquiry submitted and notification sent",
      data: result.rows[0],
    });
  } catch (err) {
    console.error(err.message);
    res.status(500).json({ error: "Server error" });
  }
};

export const submitBusinessInquiry = async (req, res) => {
  const { name, contactNumber, email, description } = req.body;

  try {
    const query = `
      INSERT INTO contact_inquiries (name,phone_number, email, description, inquiry_type, created_at)
      VALUES ($1, $2, $3, $4, $5,$6) RETURNING *`;
    const values = [
      name,
      contactNumber,
      email,
      description,
      "business",
      new Date().toISOString(),
    ];
    const result = await postgreSqlPool.query(query, values);

    await transporter.sendMail({
      from: `"Sasthra Academy" <${config.MAIL_USER}>`,
      replyTo: email,
      to: config.MAIL_TO,
      subject: `🤝 New Business Inquiry: ${name}`,
      html: `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="UTF-8">
        <title>New Business Inquiry</title>
        <style>
          @import url('https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;600;700&display=swap');
          
          body {
            font-family: 'Poppins', sans-serif;
            background-color: #f8f9fa;
            margin: 0;
            padding: 0;
          }
          
          .email-container {
            max-width: 600px;
            margin: 0 auto;
            background: linear-gradient(135deg, #ffffff 0%, #f5f7fa 100%);
            border-radius: 16px;
            overflow: hidden;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.08);
          }
          
          .header {
            background: linear-gradient(135deg, #10b981 0%, #059669 100%);
            padding: 30px;
            text-align: center;
            color: white;
          }
          
          .header h1 {
            margin: 0;
            font-size: 24px;
            font-weight: 700;
          }
          
          .header-icon {
            font-size: 40px;
            margin-bottom: 15px;
            display: inline-block;
          }
          
          .content {
            padding: 30px;
          }
          
          .details-table {
            width: 100%;
            border-collapse: separate;
            border-spacing: 0 10px;
          }
          
          .details-table td {
            padding: 12px 15px;
            vertical-align: top;
          }
          
          .details-table tr td:first-child {
            font-weight: 600;
            color: #4b5563;
            width: 35%;
            border-right: 1px solid #e5e7eb;
          }
          
          .details-table tr {
            background-color: #ffffff;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.03);
          }
          
          .message-box {
            background-color: #ffffff;
            border-left: 4px solid #10b981;
            padding: 15px;
            margin-top: 20px;
            border-radius: 0 8px 8px 0;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.03);
          }
          
          .footer {
            text-align: center;
            padding: 20px;
            color: #6b7280;
            font-size: 12px;
            background-color: #f9fafb;
            border-top: 1px solid #e5e7eb;
          }
          
          .action-button {
            display: inline-block;
            margin-top: 20px;
            padding: 12px 24px;
            background: linear-gradient(135deg, #10b981 0%, #059669 100%);
            color: white;
            text-decoration: none;
            border-radius: 50px;
            font-weight: 600;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
          }
        </style>
      </head>
      <body>
        <div class="email-container">
          <div class="header">
            <div class="header-icon">🤝</div>
            <h1>Business Inquiry</h1>
          </div>
          
          <div class="content">
            <table class="details-table">
              <tr>
                <td> Name</td>
                <td>${name}</td>
              </tr>
              <tr>
                <td>Contact</td>
                <td>${contactNumber}</td>
              </tr>
              <tr>
                <td>Email</td>
                <td><a href="mailto:${email}">${email}</a></td>
              </tr>
            </table>
            
            <div class="message-box">
              <h3 style="margin-top: 0; color: #065f46;">Business Inquiry:</h3>
              <p style="margin-bottom: 0; color: #4b5563; line-height: 1.6;">${description}</p>
            </div>
            
            <div style="text-align: center; margin-top: 30px;">
              <a href="mailto:${email}" class="action-button">Reply to Inquiry</a>
            </div>
          </div>
          
          <div class="footer">
            <p>This inquiry was submitted through Sasthra Academy's contact form.</p>
            <p> ${new Date().getFullYear()} Sasthra Academy. All rights reserved.</p>
          </div>
        </div>
      </body>
      </html>
    `,
    });

    res.status(201).json({
      message: "Business inquiry submitted and notification sent",
      data: result.rows[0],
    });
  } catch (err) {
    console.error(err);
    res.status(500).json({ error: "Server error" });
  }
};
