// index.js
import { fileURLToPath } from "url";
import path from "path";
import dotenv from "dotenv";

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Load correct .env file based on NODE_ENV
dotenv.config({
  path: path.resolve(__dirname, `${process.env.NODE_ENV}.env`),
});

import express from "express";
import cors from "cors";
import bodyParser from "body-parser";
import contactRoutes from "./router/contact.routes.js";
import { connectPostgreSQL } from "./config/postgresql.config.js";
import config from "./config.js";

const app = express();

app.use(cors());
connectPostgreSQL();

app.use(bodyParser.json());
app.use(bodyParser.urlencoded({ extended: true }));

app.use("/contact", contactRoutes);

app.listen(config.PORT, config.HOST, () => {
  console.log(`🚀 Server running on http://${config.HOST}:${config.PORT}`);
});
